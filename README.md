# GestureFlow - Core Gesture Recognition Engine

## EPIC GFLOW-E01 Implementation

This is the implementation of **EPIC GFLOW-E01: Core Gesture Recognition Engine (MVP)** for the GestureFlow project. It provides foundational capability to detect hands via webcam and recognize a basic set of static gestures.

## Implemented Tickets

### ✅ GFLOW-1: Setup Project & Integrate Webcam Input
- Python project structure initialized
- OpenCV integration for webcam access and display
- Basic application window with live webcam feed
- Error handling for webcam detection issues
- Smooth video feed (>15 FPS target)

### ✅ GFLOW-2: Integrate MediaPipe Hand Tracking
- MediaPipe Hands library integrated
- Real-time hand landmark detection (21 points per hand)
- Hand landmarks overlaid on webcam feed
- Stable tracking for multiple hands
- Minimal performance impact

### ✅ GFLOW-3: Implement Pre-defined Static Gesture Recognizer
- Rule-based recognition for 5 static gestures:
  - **Open Palm** - All fingers extended
  - **Fist** - All fingers closed
  - **Peace Sign** - Index and middle fingers up
  - **Thumbs Up** - Thumb extended upward
  - **Pointing** - Index finger extended
- Consistent recognition under typical lighting conditions
- Internal event generation for recognized gestures

### ✅ GFLOW-4: Basic Performance & Accuracy Benchmarking
- Real-time FPS monitoring
- Recognition latency measurement (milliseconds)
- Frame processing counter
- Performance metrics display in UI

## Requirements

- **Python**: 3.12.9 (or 3.9+)
- **Operating System**: Windows 10/11, macOS, or Linux
- **Hardware**: Webcam (built-in or USB)

## Installation & Setup

### 1. Create Virtual Environment
```bash
python -m venv venv
```

### 2. Activate Virtual Environment
```bash
# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Run Application
```bash
# Normal mode
python main.py

# Debug mode (shows detailed gesture recognition info)
python debug_gestures.py
```

## Usage

1. **Launch Application**: Run `python main.py`
2. **Start Webcam**: Click "Start Webcam" button
3. **Perform Gestures**: Show your hand to the camera and perform supported gestures
4. **View Recognition**: Watch the "Gesture Recognition" panel for detected gestures
5. **Monitor Performance**: Check "Performance Metrics" for system performance
6. **Stop Webcam**: Click "Stop Webcam" when finished

## Supported Gestures

| Gesture | Description | Recognition Criteria |
|---------|-------------|---------------------|
| **Open Palm** | All fingers extended | All fingertips above PIP joints |
| **Fist** | All fingers closed | All fingertips below PIP joints |
| **Peace Sign** | Index and middle up | Only index & middle extended |
| **Thumbs Up** | Thumb extended upward | Thumb above wrist, separated from other fingers, other fingers folded |
| **Pointing** | Index finger extended | Only index finger extended |

## Technical Architecture

### Components
- **WebcamThread**: Handles video capture and MediaPipe processing
- **GestureRecognizer**: Rule-based static gesture recognition
- **MainWindow**: PySide6 GUI with real-time display and controls

### Performance Targets (GFLOW-4)
- **FPS**: >15 FPS webcam feed
- **Recognition Latency**: <50ms per gesture
- **CPU Usage**: Optimized for real-time performance

## Troubleshooting

### Webcam Issues
- Ensure webcam is connected and not used by other applications
- Check webcam permissions in system settings
- Try different USB ports for external webcams

### Recognition Issues
- Ensure good lighting conditions
- Use plain background behind hand
- Hold gestures clearly for 1-2 seconds
- Keep hand within webcam frame

### Thumbs Up Not Detected
- Make sure thumb is clearly extended upward above your wrist
- Keep other fingers (index, middle, ring, pinky) curled/folded
- Ensure thumb is separated from other fingers (not touching them)
- Try holding the gesture for 2-3 seconds
- Use debug mode: `python debug_gestures.py` to see detection details

### Performance Issues
- Close other resource-intensive applications
- Reduce webcam resolution if needed
- Check system requirements

## Next Steps

This implementation completes EPIC GFLOW-E01. The next epics to implement are:

- **GFLOW-E02**: User-Defined Static Gesture Customization
- **GFLOW-E03**: Gesture-to-Action Mapping & Execution
- **GFLOW-E04**: Application Shell & User Experience

## Dependencies

- `opencv-python>=4.8.0` - Computer vision and webcam capture
- `mediapipe>=0.10.0` - Hand tracking and landmark detection
- `PySide6>=6.6.0` - Cross-platform GUI framework
- `numpy>=1.24.0` - Numerical computations

## License

This project is part of the GestureFlow development initiative.
