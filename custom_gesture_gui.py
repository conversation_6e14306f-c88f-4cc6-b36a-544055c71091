#!/usr/bin/env python3
"""
GestureFlow - Custom Gesture GUI
EPIC GFLOW-E02: User-Defined Static Gesture Customization

GFLOW-6: Develop GUI for Custom Static Gesture Recording & Labeling
GFLOW-9: Develop GUI for Managing Custom Gestures (View, Delete)
GFLOW-10: Implement Basic Gesture Ambiguity Warning

This module provides the GUI for recording, training, and managing custom gestures.
"""

import sys
import time
import numpy as np
from typing import List, Tuple, Optional
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QWidget, 
                               QPushButton, QLabel, QLineEdit, QTextEdit, 
                               QListWidget, QListWidgetItem, QProgressBar,
                               QMessageBox, QComboBox, QFrame, QGroupBox,
                               QSpinBox, QCheckBox, QTabWidget)
from PySide6.QtCore import QTimer, QThread, Signal, Qt
from PySide6.QtGui import <PERSON>Font, QPixmap, QImage

from gesture_data import CustomGestureData, CustomGestureStorage
from gesture_training import GestureTrainer


class GestureRecordingThread(QThread):
    """
    Thread for recording gesture samples
    """
    sample_recorded = Signal(list)  # landmarks
    recording_progress = Signal(int, int)  # current, total
    recording_finished = Signal()
    
    def __init__(self, webcam_thread):
        super().__init__()
        self.webcam_thread = webcam_thread
        self.recording = False
        self.samples = []
        self.target_samples = 10
        self.sample_interval = 0.5  # seconds between samples
        
    def start_recording(self, target_samples: int = 10):
        """Start recording gesture samples"""
        self.target_samples = target_samples
        self.samples = []
        self.recording = True
        self.start()
    
    def stop_recording(self):
        """Stop recording"""
        self.recording = False
        
    def run(self):
        """Recording loop"""
        last_sample_time = 0
        
        while self.recording and len(self.samples) < self.target_samples:
            current_time = time.time()
            
            # Check if enough time has passed since last sample
            if current_time - last_sample_time >= self.sample_interval:
                # Get current hand landmarks from webcam thread
                if hasattr(self.webcam_thread, 'latest_landmarks') and self.webcam_thread.latest_landmarks:
                    landmarks = self.webcam_thread.latest_landmarks[0]  # First hand
                    self.samples.append(landmarks)
                    self.sample_recorded.emit(landmarks)
                    self.recording_progress.emit(len(self.samples), self.target_samples)
                    last_sample_time = current_time
            
            self.msleep(100)  # Check every 100ms
        
        self.recording = False
        self.recording_finished.emit()


class CustomGestureDialog(QDialog):
    """
    Dialog for managing custom gestures
    """
    
    def __init__(self, parent=None, webcam_thread=None):
        super().__init__(parent)
        self.webcam_thread = webcam_thread
        self.storage = CustomGestureStorage()
        self.trainer = GestureTrainer(self.storage)
        self.recording_thread = GestureRecordingThread(webcam_thread) if webcam_thread else None
        
        self.setWindowTitle("Custom Gesture Manager")
        self.setGeometry(200, 200, 800, 600)
        self.setModal(True)
        
        self.setup_ui()
        self.connect_signals()
        self.refresh_gesture_list()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Tab 1: Record New Gesture (GFLOW-6)
        self.setup_recording_tab()
        
        # Tab 2: Manage Gestures (GFLOW-9)
        self.setup_management_tab()
        
        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)
    
    def setup_recording_tab(self):
        """Setup the gesture recording tab (GFLOW-6)"""
        recording_widget = QWidget()
        layout = QVBoxLayout(recording_widget)
        
        # Title
        title = QLabel("Record New Custom Gesture")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        # Gesture details
        details_group = QGroupBox("Gesture Details")
        details_layout = QVBoxLayout(details_group)
        
        # Name input
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("Gesture Name:"))
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Enter a unique name for your gesture")
        name_layout.addWidget(self.name_input)
        details_layout.addLayout(name_layout)
        
        # Description input
        desc_layout = QHBoxLayout()
        desc_layout.addWidget(QLabel("Description:"))
        self.desc_input = QLineEdit()
        self.desc_input.setPlaceholderText("Optional description")
        desc_layout.addWidget(self.desc_input)
        details_layout.addLayout(desc_layout)
        
        layout.addWidget(details_group)
        
        # Recording settings
        settings_group = QGroupBox("Recording Settings")
        settings_layout = QVBoxLayout(settings_group)
        
        # Number of samples
        samples_layout = QHBoxLayout()
        samples_layout.addWidget(QLabel("Number of samples:"))
        self.samples_spinbox = QSpinBox()
        self.samples_spinbox.setRange(5, 50)
        self.samples_spinbox.setValue(10)
        samples_layout.addWidget(self.samples_spinbox)
        samples_layout.addWidget(QLabel("(More samples = better accuracy)"))
        settings_layout.addLayout(samples_layout)
        
        # Model type
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("ML Model:"))
        self.model_combo = QComboBox()
        available_models = self.trainer.get_available_models()
        for model_id, model_info in available_models.items():
            self.model_combo.addItem(f"{model_info['name']} - {model_info['description']}", model_id)
        model_layout.addWidget(self.model_combo)
        settings_layout.addLayout(model_layout)
        
        layout.addWidget(settings_group)
        
        # Recording controls
        controls_group = QGroupBox("Recording")
        controls_layout = QVBoxLayout(controls_group)
        
        # Instructions
        instructions = QTextEdit()
        instructions.setMaximumHeight(80)
        instructions.setReadOnly(True)
        instructions.setText(
            "1. Enter a gesture name above\n"
            "2. Click 'Start Recording' and perform your gesture multiple times\n"
            "3. Hold each gesture clearly for about 1 second\n"
            "4. Click 'Train Model' when recording is complete"
        )
        controls_layout.addWidget(instructions)
        
        # Recording buttons
        button_layout = QHBoxLayout()
        self.record_button = QPushButton("Start Recording")
        self.stop_button = QPushButton("Stop Recording")
        self.stop_button.setEnabled(False)
        self.train_button = QPushButton("Train Model")
        self.train_button.setEnabled(False)
        
        button_layout.addWidget(self.record_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.train_button)
        controls_layout.addLayout(button_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to record")
        controls_layout.addWidget(self.status_label)
        
        layout.addWidget(controls_group)
        
        self.tab_widget.addTab(recording_widget, "Record New Gesture")
    
    def setup_management_tab(self):
        """Setup the gesture management tab (GFLOW-9)"""
        management_widget = QWidget()
        layout = QVBoxLayout(management_widget)
        
        # Title
        title = QLabel("Manage Custom Gestures")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)
        
        # Gesture list
        list_group = QGroupBox("Your Custom Gestures")
        list_layout = QVBoxLayout(list_group)
        
        self.gesture_list = QListWidget()
        list_layout.addWidget(self.gesture_list)
        
        # Management buttons
        button_layout = QHBoxLayout()
        self.refresh_button = QPushButton("Refresh")
        self.delete_button = QPushButton("Delete Selected")
        self.toggle_button = QPushButton("Enable/Disable")
        self.retrain_button = QPushButton("Retrain Selected")
        
        button_layout.addWidget(self.refresh_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.toggle_button)
        button_layout.addWidget(self.retrain_button)
        list_layout.addLayout(button_layout)
        
        layout.addWidget(list_group)
        
        # Gesture details
        details_group = QGroupBox("Gesture Details")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)
        details_layout.addWidget(self.details_text)
        
        layout.addWidget(details_group)
        
        self.tab_widget.addTab(management_widget, "Manage Gestures")
    
    def connect_signals(self):
        """Connect UI signals"""
        # Recording tab
        self.record_button.clicked.connect(self.start_recording)
        self.stop_button.clicked.connect(self.stop_recording)
        self.train_button.clicked.connect(self.train_gesture)
        
        # Management tab
        self.refresh_button.clicked.connect(self.refresh_gesture_list)
        self.delete_button.clicked.connect(self.delete_selected_gesture)
        self.toggle_button.clicked.connect(self.toggle_selected_gesture)
        self.retrain_button.clicked.connect(self.retrain_selected_gesture)
        self.gesture_list.itemSelectionChanged.connect(self.show_gesture_details)
        
        # Recording thread
        if self.recording_thread:
            self.recording_thread.sample_recorded.connect(self.on_sample_recorded)
            self.recording_thread.recording_progress.connect(self.on_recording_progress)
            self.recording_thread.recording_finished.connect(self.on_recording_finished)
    
    def start_recording(self):
        """Start recording gesture samples (GFLOW-6)"""
        if not self.webcam_thread:
            QMessageBox.warning(self, "Error", "Webcam not available for recording")
            return
        
        gesture_name = self.name_input.text().strip()
        if not gesture_name:
            QMessageBox.warning(self, "Error", "Please enter a gesture name")
            return
        
        # Check for duplicate names
        existing_gestures = self.storage.get_all_gestures()
        for gesture in existing_gestures.values():
            if gesture.name.lower() == gesture_name.lower():
                QMessageBox.warning(self, "Error", f"A gesture named '{gesture_name}' already exists")
                return
        
        # Start recording
        target_samples = self.samples_spinbox.value()
        self.recording_thread.start_recording(target_samples)
        
        # Update UI
        self.record_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(target_samples)
        self.progress_bar.setValue(0)
        self.status_label.setText(f"Recording... Perform your '{gesture_name}' gesture clearly")
    
    def stop_recording(self):
        """Stop recording"""
        if self.recording_thread:
            self.recording_thread.stop_recording()
    
    def on_sample_recorded(self, landmarks):
        """Handle when a sample is recorded"""
        # Could add visual feedback here
        pass
    
    def on_recording_progress(self, current, total):
        """Update recording progress"""
        self.progress_bar.setValue(current)
        self.status_label.setText(f"Recording... {current}/{total} samples captured")
    
    def on_recording_finished(self):
        """Handle when recording is finished"""
        self.record_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.train_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        samples_count = len(self.recording_thread.samples)
        self.status_label.setText(f"Recording complete! {samples_count} samples captured. Ready to train.")
    
    def train_gesture(self):
        """Train the recorded gesture (GFLOW-7)"""
        gesture_name = self.name_input.text().strip()
        gesture_desc = self.desc_input.text().strip()
        
        if not gesture_name or not self.recording_thread.samples:
            QMessageBox.warning(self, "Error", "No samples to train")
            return
        
        try:
            # Create gesture data
            gesture_data = CustomGestureData(gesture_name, gesture_desc)
            gesture_data.samples_count = len(self.recording_thread.samples)
            
            # Save gesture and samples
            self.storage.add_gesture(gesture_data)
            self.storage.save_samples(gesture_data.gesture_id, self.recording_thread.samples)
            
            # Train model
            model_type = self.model_combo.currentData()
            self.status_label.setText("Training model... Please wait")
            
            # Train in background (this might take a moment)
            results = self.trainer.train_single_gesture(gesture_data.gesture_id, model_type)
            
            if results['success']:
                # Check for ambiguity (GFLOW-10)
                self.check_gesture_ambiguity(gesture_data.gesture_id)
                
                QMessageBox.information(
                    self, "Success", 
                    f"Gesture '{gesture_name}' trained successfully!\n"
                    f"Test accuracy: {results['test_accuracy']:.1%}\n"
                    f"Model: {results['model_type']}"
                )
                
                # Reset UI
                self.name_input.clear()
                self.desc_input.clear()
                self.train_button.setEnabled(False)
                self.status_label.setText("Ready to record")
                self.refresh_gesture_list()
                
            else:
                QMessageBox.critical(self, "Training Failed", f"Error: {results.get('error', 'Unknown error')}")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to train gesture: {str(e)}")
    
    def check_gesture_ambiguity(self, gesture_id: str):
        """Check for gesture ambiguity (GFLOW-10)"""
        try:
            all_gestures = self.storage.get_all_gestures()
            similar_gestures = []
            
            for other_id, other_gesture in all_gestures.items():
                if other_id == gesture_id or not other_gesture.enabled:
                    continue
                
                similarity = self.trainer.evaluate_gesture_similarity(gesture_id, other_id)
                if similarity > 0.8:  # High similarity threshold
                    similar_gestures.append((other_gesture.name, similarity))
            
            if similar_gestures:
                gesture_name = self.storage.get_gesture(gesture_id).name
                warning_msg = f"Warning: '{gesture_name}' is very similar to:\n\n"
                for name, sim in similar_gestures:
                    warning_msg += f"• {name} (similarity: {sim:.1%})\n"
                warning_msg += "\nThis may cause recognition conflicts. Consider retraining with more distinct gestures."
                
                QMessageBox.warning(self, "Gesture Ambiguity Warning", warning_msg)
        
        except Exception as e:
            print(f"Error checking ambiguity: {e}")
    
    def refresh_gesture_list(self):
        """Refresh the gesture list (GFLOW-9)"""
        self.gesture_list.clear()
        
        gestures = self.storage.get_all_gestures()
        for gesture_id, gesture in gestures.items():
            status = "✓" if gesture.enabled else "✗"
            accuracy = f"{gesture.training_accuracy:.1%}" if gesture.training_accuracy > 0 else "N/A"
            
            item_text = f"{status} {gesture.name} (Accuracy: {accuracy}, Samples: {gesture.samples_count})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, gesture_id)
            self.gesture_list.addItem(item)
    
    def show_gesture_details(self):
        """Show details of selected gesture"""
        current_item = self.gesture_list.currentItem()
        if not current_item:
            self.details_text.clear()
            return
        
        gesture_id = current_item.data(Qt.UserRole)
        gesture = self.storage.get_gesture(gesture_id)
        
        if gesture:
            details = f"Name: {gesture.name}\n"
            details += f"Description: {gesture.description or 'None'}\n"
            details += f"Created: {gesture.created_date}\n"
            details += f"Samples: {gesture.samples_count}\n"
            details += f"Training Accuracy: {gesture.training_accuracy:.1%}\n"
            details += f"Status: {'Enabled' if gesture.enabled else 'Disabled'}\n"
            details += f"Model Path: {gesture.model_path or 'None'}"
            
            self.details_text.setText(details)
    
    def delete_selected_gesture(self):
        """Delete selected gesture (GFLOW-9)"""
        current_item = self.gesture_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Error", "Please select a gesture to delete")
            return
        
        gesture_id = current_item.data(Qt.UserRole)
        gesture = self.storage.get_gesture(gesture_id)
        
        if gesture:
            reply = QMessageBox.question(
                self, "Confirm Delete",
                f"Are you sure you want to delete '{gesture.name}'?\n"
                "This action cannot be undone.",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.storage.remove_gesture(gesture_id):
                    QMessageBox.information(self, "Success", f"Gesture '{gesture.name}' deleted")
                    self.refresh_gesture_list()
                else:
                    QMessageBox.critical(self, "Error", "Failed to delete gesture")
    
    def toggle_selected_gesture(self):
        """Toggle enabled/disabled state of selected gesture"""
        current_item = self.gesture_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Error", "Please select a gesture")
            return
        
        gesture_id = current_item.data(Qt.UserRole)
        gesture = self.storage.get_gesture(gesture_id)
        
        if gesture:
            gesture.enabled = not gesture.enabled
            self.storage.add_gesture(gesture)  # Update storage
            self.refresh_gesture_list()
            
            status = "enabled" if gesture.enabled else "disabled"
            QMessageBox.information(self, "Success", f"Gesture '{gesture.name}' {status}")
    
    def retrain_selected_gesture(self):
        """Retrain selected gesture"""
        current_item = self.gesture_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Error", "Please select a gesture to retrain")
            return
        
        gesture_id = current_item.data(Qt.UserRole)
        gesture = self.storage.get_gesture(gesture_id)
        
        if gesture:
            reply = QMessageBox.question(
                self, "Confirm Retrain",
                f"Retrain '{gesture.name}' with existing samples?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    results = self.trainer.train_single_gesture(gesture_id, 'svm')
                    
                    if results['success']:
                        QMessageBox.information(
                            self, "Success",
                            f"Gesture '{gesture.name}' retrained!\n"
                            f"New accuracy: {results['test_accuracy']:.1%}"
                        )
                        self.refresh_gesture_list()
                    else:
                        QMessageBox.critical(self, "Error", f"Retraining failed: {results.get('error', 'Unknown error')}")
                
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Retraining failed: {str(e)}")
