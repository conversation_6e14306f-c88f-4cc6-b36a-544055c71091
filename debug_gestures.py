#!/usr/bin/env python3
"""
GestureFlow Debug Mode
Enable debug output for gesture recognition testing

Run this instead of main.py to see detailed gesture recognition debug info.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Enable debug mode
from config import GESTURE_CONFIG
GESTURE_CONFIG['debug_mode'] = True

# Import and run main application
from main import main

if __name__ == "__main__":
    print("🐛 GestureFlow Debug Mode Enabled")
    print("=" * 40)
    print("Debug output will be shown for gesture recognition.")
    print("This helps troubleshoot gesture detection issues.")
    print("=" * 40)
    main()
