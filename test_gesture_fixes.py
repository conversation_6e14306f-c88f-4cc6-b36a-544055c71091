#!/usr/bin/env python3
"""
Test script to verify the gesture recognition fixes:
1. New gestures recognized immediately after training
2. Disabled gestures are not detected
"""

import sys
import time
from gesture_data import CustomGestureData, CustomGestureStorage
from gesture_training import GestureTrainer
from main import GestureRecognizer

def test_gesture_fixes():
    """Test both gesture recognition fixes"""
    print("🧪 Testing Gesture Recognition Fixes")
    print("=" * 50)
    
    # Initialize components
    storage = CustomGestureStorage()
    trainer = GestureTrainer(storage)
    recognizer = GestureRecognizer()
    
    print(f"Initial state: {len(recognizer.custom_models)} custom models loaded")
    
    # Test 1: Check if existing gestures are loaded correctly
    print("\n📋 Test 1: Model Loading Status")
    all_gestures = storage.get_all_gestures()
    for gesture_id, gesture in all_gestures.items():
        model = storage.load_model(gesture_id)
        status = "✅ Loaded" if model else "❌ Failed to load"
        enabled = "🟢 Enabled" if gesture.enabled else "🔴 Disabled"
        print(f"  {gesture.name}: {status}, {enabled}")
    
    # Test 2: Verify model reloading works
    print("\n🔄 Test 2: Model Reloading")
    initial_count = len(recognizer.custom_models)
    recognizer.reload_custom_models()
    after_reload_count = len(recognizer.custom_models)
    print(f"  Before reload: {initial_count} models")
    print(f"  After reload: {after_reload_count} models")
    print(f"  Reload working: {'✅ Yes' if after_reload_count >= initial_count else '❌ No'}")
    
    # Test 3: Check enabled/disabled status handling
    print("\n🎯 Test 3: Enabled/Disabled Status Check")
    for gesture_id in recognizer.custom_models.keys():
        gesture_data = storage.get_gesture(gesture_id)
        if gesture_data:
            print(f"  {gesture_data.name}: {'🟢 Enabled' if gesture_data.enabled else '🔴 Disabled'}")
            
            # Test recognition with fake landmarks (just for status check)
            fake_landmarks = [(0.5, 0.5, 0.0) for _ in range(21)]  # 21 fake landmarks
            try:
                result = recognizer._recognize_custom_gesture(fake_landmarks)
                if gesture_data.enabled:
                    print(f"    Recognition test: {'✅ Can be recognized' if result is not None else '⚠️ Not recognized (normal - fake data)'}")
                else:
                    print(f"    Recognition test: {'✅ Correctly skipped (disabled)' if result is None else '❌ Still being recognized!'}")
            except Exception as e:
                print(f"    Recognition test: ⚠️ Error (expected with fake data): {e}")
    
    print("\n📊 Summary:")
    print(f"  Total gestures in storage: {len(all_gestures)}")
    print(f"  Models loaded in recognizer: {len(recognizer.custom_models)}")
    enabled_gestures = [g for g in all_gestures.values() if g.enabled]
    disabled_gestures = [g for g in all_gestures.values() if not g.enabled]
    print(f"  Enabled gestures: {len(enabled_gestures)}")
    print(f"  Disabled gestures: {len(disabled_gestures)}")
    
    print("\n✅ Test completed! Check the output above to verify:")
    print("  1. Models reload correctly after dialog closes")
    print("  2. Disabled gestures are skipped during recognition")
    print("  3. All gesture statuses are properly tracked")

if __name__ == "__main__":
    test_gesture_fixes()
